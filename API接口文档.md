# 内部API接口文档

## 用户模块

### 登录接口
- **地址：** /re-mpd/api/account/login
- **类型：** POST
- **状态码：** 200
- **简介：** 用户登录认证接口
- **请求接口格式：**
  ```json
  {
    "username": "String (用户名)",
    "password": "String (密码，加密后)",
    "authType": "String (登录方式，默认ACCOUNT)"
  }
  ```
- **返回接口格式：**
  ```json
  {
    "code": "Number (状态码)",
    "message": "String (返回消息)",
    "data": {
      "token": "String (访问令牌)",
      "tokenType": "String (令牌类型，如Bearer)",
      "tokenName": "String (令牌名称)"
    }
  }
  ```

### 获取用户信息接口
- **地址：** /re-mpd/api/account/info
- **类型：** GET
- **状态码：** 200
- **简介：** 获取当前登录用户的详细信息
- **请求接口格式：**
  - **Headers：** Authorization: Bearer {token}
- **返回接口格式：**
  ```json
  {
    "code": "Number (状态码)",
    "message": "String (返回消息)",
    "data": {
      "id": "Number (用户ID)",
      "organizationId": "Number (机构ID)",
      "username": "String (用户名)",
      "name": "String (姓名)",
      "email": "String (邮箱)",
      "gender": "Number (性别：1=男, 2=女)",
      "phoneCountryCode": "Number (手机国家代码)",
      "phoneNumber": "String (手机号)",
      "avatar": "String (头像URL)",
      "description": "String (描述)",
      "status": "Number (状态：1=正常, 2=禁用)",
      "language": "String (语言)",
      "timezone": "String (时区)"
    }
  }
  ```

### 用户登出接口
- **地址：** /re-mpd/api/account/logout
- **类型：** POST
- **状态码：** 200
- **简介：** 用户登出接口
- **请求接口格式：**
  - **Headers：** Authorization: Bearer {token}
- **返回接口格式：**
  ```json
  {
    "code": "Number (状态码)",
    "message": "String (返回消息)"
  }
  ```

### 修改用户信息接口
- **地址：** /re-mpd/api/account/basic/info
- **类型：** PATCH
- **状态码：** 200
- **简介：** 修改用户基本信息
- **请求接口格式：**
  - **Headers：** Authorization: Bearer {token}
  - **Body：** JSON格式的用户信息
- **返回接口格式：**
  ```json
  {
    "code": "Number (状态码)",
    "message": "String (返回消息)"
  }
  ```

## 患者管理模块

### 患者列表接口
- **地址：** /re-mpd/api/patient
- **类型：** GET
- **状态码：** 200
- **简介：** 分页查询患者列表，支持条件筛选
- **请求接口格式：**
  - **Headers：** Authorization: Bearer {token}
  - **Query参数：**
    - `page`: Number (页码，从1开始)
    - `size`: Number (每页条数，示例值10)
    - `sort`: String (排序条件，示例值：createTime,desc)
    - `gender`: Number (性别：0=未知, 1=男, 2=女，可选)
    - `keywords`: String (搜索关键词，可选)
- **返回接口格式：**
  ```json
  {
    "code": "Number (状态码)",
    "message": "String (返回消息)",
    "data": {
      "total": "Number (总记录数)",
      "list": [
        {
          "id": "Number (患者ID)",
          "name": "String (患者姓名)",
          "gender": "Number (性别：1=男, 2=女)",
          "birthday": "String (出生日期)",
          "age": "Number (年龄)",
          "phoneNumber": "String (手机号)",
          "createTime": "String (创建日期)",
          "updateTime": "String (修改日期)",
          "organizationId": "Number (机构ID)"
        }
      ]
    }
  }
  ```

### 添加患者接口
- **地址：** /re-mpd/api/patient
- **类型：** POST
- **状态码：** 200
- **简介：** 添加新患者
- **请求接口格式：**
  - **Headers：** Authorization: Bearer {token}
  - **Body：**
    ```json
    {
      "name": "String (姓名)",
      "gender": "Number (性别：1=男, 2=女)",
      "birthday": "String (生日，格式：2023-04-23)",
      "phoneNumber": "String (手机号)"
    }
    ```
- **返回接口格式：**
  ```json
  {
    "code": "Number (状态码)",
    "message": "String (返回消息)",
    "data": {
      "id": "String (患者ID)"
    }
  }
  ```

### 查询患者信息接口
- **地址：** /re-mpd/api/patient/{id}
- **类型：** GET
- **状态码：** 200
- **简介：** 根据ID查询患者详细信息
- **请求接口格式：**
  - **Headers：** Authorization: Bearer {token}
  - **Path参数：** id (患者ID)
- **返回接口格式：**
  ```json
  {
    "code": "Number (状态码)",
    "message": "String (返回消息)",
    "data": {
      "id": "Number (患者ID)",
      "name": "String (患者姓名)",
      "gender": "Number (性别：1=男, 2=女)",
      "birthday": "String (出生日期)",
      "age": "Number (年龄)",
      "phoneNumber": "String (手机号)",
      "createTime": "String (创建日期)",
      "updateTime": "String (修改日期)",
      "organizationId": "Number (机构ID)"
    }
  }
  ```

### 修改患者接口
- **地址：** /re-mpd/api/patient/{id}
- **类型：** PUT
- **状态码：** 200
- **简介：** 修改患者信息
- **请求接口格式：**
  - **Headers：** Authorization: Bearer {token}
  - **Path参数：** id (患者ID)
  - **Body：** JSON格式的患者信息
- **返回接口格式：**
  ```json
  {
    "code": "Number (状态码)",
    "message": "String (返回消息)"
  }
  ```

### 删除患者接口
- **地址：** /re-mpd/api/patient
- **类型：** DELETE
- **状态码：** 200
- **简介：** 删除患者（支持批量删除）
- **请求接口格式：**
  - **Headers：** Authorization: Bearer {token}
  - **Body：**
    ```json
    {
      "ids": ["Number (患者ID列表)"]
    }
    ```
- **返回接口格式：**
  ```json
  {
    "code": "Number (状态码)",
    "message": "String (返回消息)"
  }
  ```

## 评估报告模块

### 评估列表接口
- **地址：** /re-mpd/api/assessment
- **类型：** GET
- **状态码：** 200
- **简介：** 分页查询评估报告列表，支持条件筛选
- **请求接口格式：**
  - **Headers：** Authorization: Bearer {token}
  - **Query参数：**
    - `page`: Number (页码，从1开始)
    - `size`: Number (每页条数，示例值10)
    - `sort`: String (排序条件，示例值：createTime,desc)
    - `gender`: Number (性别：0=未知, 1=男, 2=女，可选)
    - `keywords`: String (搜索关键词，可选)
- **返回接口格式：**
  ```json
  {
    "code": "Number (状态码)",
    "message": "String (返回消息)",
    "data": {
      "total": "Number (总记录数)",
      "list": [
        {
          "id": "Number (报告ID)",
          "name": "String (患者姓名)",
          "gender": "Number (性别：1=男, 2=女)",
          "age": "Number (年龄)",
          "createTime": "String (评估日期)",
          "patientId": "Number (患者ID)",
          "reportUrl": "String (评估报告结果URL)"
        }
      ]
    }
  }
  ```

### 提交评估数据接口
- **地址：** /re-mpd/api/assessment/rppg
- **类型：** POST
- **状态码：** 200
- **简介：** 提交PPG评估数据
- **请求接口格式：**
  - **Headers：** Authorization: Bearer {token}
  - **Body：**
    ```json
    {
      "patientId": "Number (患者ID)",
      "timeDomain": {
        "meanNN": "Number (平均RR间期)",
        "sdnn": "Number (RR间期标准差)",
        "rmssd": "Number (相邻RR间期差值的均方根)",
        "sdsd": "Number (相邻RR间期差值的标准差)",
        "pnn50": "Number (相邻RR间期差值>50ms的比例)"
      },
      "frequencyDomain": {
        "totalPower": "Number (总功率 0.0-0.4 Hz)",
        "vlf": "Number (极低频功率 0.003-0.04 Hz)",
        "lf": "Number (低频功率 0.04-0.15 Hz)",
        "hf": "Number (高频功率 0.15-0.4 Hz)",
        "lfHfRatio": "Number (LF/HF比值)",
        "stepPower": ["Number (分段功率数组)"]
      },
      "rrIntervals": ["Number (RR间期数组)"],
      "validIntervals": "Number (有效间期数)",
      "totalIntervals": "Number (总间期数)"
    }
    ```
- **返回接口格式：**
  ```json
  {
    "code": "Number (状态码)",
    "message": "String (返回消息)",
    "data": {
      "reportId": "String (报告ID)"
    }
  }
  ```

### 删除评估接口
- **地址：** /re-mpd/api/assessment
- **类型：** DELETE
- **状态码：** 200
- **简介：** 删除评估报告（支持批量删除）
- **请求接口格式：**
  - **Headers：** Authorization: Bearer {token}
  - **Body：**
    ```json
    {
      "ids": ["Number (评估ID列表)"]
    }
    ```
- **返回接口格式：**
  ```json
  {
    "code": "Number (状态码)",
    "message": "String (返回消息)"
  }
  ```

## 通用请求头

所有API请求都会自动添加以下通用请求头：

- `X-Device-Sn`: String (设备序列号)
- `X-App-Version`: String (应用版本号)
- `X-Device-Mode`: String (设备型号)
- `X-Airdoc-Client`: String (客户端标识)
- `Accept-Language`: String (语言设置，格式：zh-CN)
- `Content-Type`: application/json;charset=UTF-8

## 服务器配置

- **测试环境：** https://test-re-mpd.babyeye.com
- **生产环境：** https://re-mpd.babyeye.com
- **连接超时：** 10秒
- **读取超时：** 15秒
- **写入超时：** 15秒

## 错误处理

- **401：** 登录失效，自动跳转到登录页面
- **其他错误：** 返回对应的错误码和错误信息
