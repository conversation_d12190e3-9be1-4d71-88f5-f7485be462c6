# HRV注册App数据接口描述文档

## 一、设备端数据接口

### 1. 蓝牙设备连接接口
**接口名称：** BluetoothDeviceConnectionInterface
**API名称：** BleServiceHelper.connect()
**预期用户：** 设备操作者
**使用场景：** 需要连接脉搏血氧饱和度仪进行心率变异性数据采集时
**预期用途：** 实现与PC60FW等蓝牙设备的连接，获取PPG信号数据用于HRV分析

**技术特征：**
- 通信协议：蓝牙低功耗（BLE）4.0及以上
- 数据传输：实时数据流传输，采样频率125Hz
- 数据格式：PPG原始信号值（16位整数数组）
- 连接方式：UUID服务发现和特征值订阅

**API参数：**
```kotlin
// 设备扫描API
BleServiceHelper.startScan(models: IntArray)
// 输入参数：models - 设备型号数组 [Bluetooth.MODEL_PC60FW]

// 设备连接API
BleServiceHelper.connect(context: Context, model: Int, device: BluetoothDevice)
// 输入参数：
// - context: Android应用上下文
// - model: 设备型号 (Bluetooth.MODEL_PC60FW = 0x15)
// - device: 蓝牙设备对象

// 状态回调接口
interface BleChangeObserver {
    fun onBleStateChanged(model: Int, state: Int)
    // state: 连接状态 (0=断开, 1=连接中, 2=已连接, -1=失败)
}
```

**使用限制：** 仅支持PC60FW型号设备，需要Android 6.0以上系统，需要蓝牙和位置权限
**故障应对措施：** 当连接中断时，自动尝试重连；连接超时30秒后提示重新连接；设备未找到时引导用户检查设备电源和距离

### 2. 实时参数监听接口
**接口名称：** RealTimeParameterInterface
**API名称：** LiveEventBus.get<InterfaceEvent>(InterfaceEvent.PC60Fw.EventPC60FwRtParam)
**预期用户：** 设备操作者
**使用场景：** 连接设备后需要实时显示血氧、脉率等参数时
**预期用途：** 实时获取设备测量的生理参数，用于界面显示和状态监控

**技术特征：**
- 更新频率：实时更新
- 数据类型：整数和浮点数
- 传输方式：事件总线机制
- 显示用途：界面实时参数显示

**API参数：**
```kotlin
// 实时参数监听API
LiveEventBus.get<InterfaceEvent>(InterfaceEvent.PC60Fw.EventPC60FwRtParam)
    .observe(this) { interfaceEvent ->
        val data = interfaceEvent.data
        Logger.d(TAG, msg = "EventPC60FwRtParam data = $data")
        if (data is RtParam) {
            // 更新界面显示
            binding.tvSpo2.text = getString(R.string.str_spo2_d, data.spo2)
            binding.tvPr.text = getString(R.string.str_pr_d, data.pr)
            binding.tvPi.text = getString(R.string.str_pi_f, data.pi)
        }
    }

// 实时参数数据结构
data class RtParam(
    val spo2: Int,          // 血氧饱和度 (%)
    val pr: Int,            // 脉率 (bpm)
    val pi: Float           // 灌注指数
)
```

**使用限制：** 需要设备连接成功后才能接收数据
**故障应对措施：** 设备断开时停止更新；数据异常时显示默认值；连接恢复后自动恢复更新

### 3. PPG波形数据采集接口
**接口名称：** PPGWaveDataCollectionInterface
**API名称：** LiveEventBus.get<InterfaceEvent>(InterfaceEvent.PC60Fw.EventPC60FwRtWave)
**预期用户：** 设备操作者
**使用场景：** 连接设备后进行心率变异性测量时
**预期用途：** 实时采集PPG波形数据，进行心率变异性分析计算

**技术特征：**
- 采样频率：50Hz（间隔20毫秒）
- 数据精度：16位整数数组
- 传输方式：事件总线机制
- 缓存机制：支持实时波形显示和数据缓存

**API参数：**
```kotlin
// PPG波形数据监听API
LiveEventBus.get<InterfaceEvent>(InterfaceEvent.PC60Fw.EventPC60FwRtWave)
    .observe(this) { interfaceEvent ->
        val data = interfaceEvent.data
        if (data is RtWave) {
            Logger.d(TAG, msg = "EventPC60FwRtWave waveIntData = ${gson.toJson(data.waveIntData)}")
            if (!isComplete && !isTerminate) {
                val ints = data.waveIntData.toList()
                ints.forEachIndexed { index, value ->
                    if (ppgDataPointList.isEmpty()) {
                        // 首个数据点使用当前时间戳
                        ppgDataPointList.add(PPGDataPoint(
                            value.toDouble(),
                            System.currentTimeMillis() * 1_000_000L + System.nanoTime() % 1_000_000L
                        ))
                    } else {
                        // 后续数据点间隔20毫秒（转换为纳秒）
                        ppgDataPointList.add(PPGDataPoint(
                            value.toDouble(),
                            ppgDataPointList[ppgDataPointList.size - 1].timestamp + 20 * 1_000_000L
                        ))
                    }
                }
                // 更新波形显示
                binding.ecgView.addDataPoints(ints)
            }
        }
    }

// PPG波形数据结构
data class RtWave(
    val waveIntData: IntArray  // PPG波形数据数组，整数格式
)

// 转换为标准PPG数据点
data class PPGDataPoint(
    val ppgValue: Double,   // PPG信号值
    val timestamp: Long     // 时间戳（纳秒）
)
```

**使用限制：** 测量时间需要5-10分钟，要求患者保持静止状态，测量过程中不能中断
**故障应对措施：** 信号质量差时提示重新佩戴设备；数据丢失时自动补偿；异常值自动过滤；测量完成或终止时停止数据采集

### 4. 评估报告打印接口
**接口名称：** EvaluationReportPrintInterface
**API名称：** PrintManager.print()
**预期用户：** 设备操作者
**使用场景：** 评估结束需要打印报告时通过WIFI打印机打印报告
**预期用途：** 实现评估报告打印

**技术特征：**
- 通信协议：WiFi网络连接，支持IPP/AirPrint协议
- 数据格式：PDF文档，通过WebView生成
- 打印规格：A4纸张，彩色打印，600DPI分辨率
- 打印框架：Android PrintManager系统服务

**API参数：**
```kotlin
// 打印管理器初始化
val printManager = getSystemService(PRINT_SERVICE) as PrintManager

// 创建打印任务API
printManager.print(
    jobName: String,                    // 打印任务名称
    printAdapter: PrintDocumentAdapter, // 打印适配器
    attributes: PrintAttributes         // 打印属性
)

// 打印属性配置
val printAttributes = PrintAttributes.Builder()
    .setMediaSize(PrintAttributes.MediaSize.ISO_A4)        // 纸张规格：A4
    .setColorMode(PrintAttributes.COLOR_MODE_COLOR)        // 颜色模式：彩色
    .setResolution(PrintAttributes.Resolution("pdf", "pdf", 600, 600)) // 分辨率：600DPI
    .setMinMargins(PrintAttributes.Margins.NO_MARGINS)     // 边距：无边距
    .build()

// WebView打印适配器
val printAdapter = webView.createPrintDocumentAdapter(jobName)

// 打印状态监控
interface PrintJobCallback {
    fun onPrintJobQueued(jobId: String)      // 任务排队
    fun onPrintJobStarted(jobId: String)     // 开始打印
    fun onPrintJobCompleted(jobId: String)   // 打印完成
    fun onPrintJobFailed(jobId: String, error: String)  // 打印失败
    fun onPrintJobCancelled(jobId: String)   // 任务取消
}
```

**使用限制：** 仅当安装了对应的WIFI打印机驱动软件后才可以正常打印
**故障应对措施：** 当打印过程中中断，进行重新连接后即可重新打印；打印机离线时提示检查网络连接；打印队列满时等待后重试

## 二、服务器端数据接口

### 1. 患者数据管理接口
**接口名称：** 患者数据管理接口  
**预期用户：** 医护人员、系统管理员  
**使用场景：** 需要管理患者基本信息和查询患者记录时  
**预期用途：** 提供患者信息的增删改查功能，支持患者档案管理  
**技术特征：** RESTful API设计，HTTPS加密传输，支持分页查询和条件筛选  
**使用限制：** 需要有效的授权令牌，单次查询最多返回100条记录  
**故障应对措施：** 令牌过期时返回401错误提示重新登录；服务器异常时返回503错误并记录日志；网络超时时自动重试3次

### 2. 评估数据上报接口
**接口名称：** 评估数据上报接口  
**预期用户：** 设备端应用程序  
**使用场景：** HRV评估完成后需要将结果上传到服务器时  
**预期用途：** 上传HRV分析结果，生成评估报告，支持数据统计分析  
**技术特征：** JSON格式数据传输，包含时域和频域参数，支持批量上传  
**使用限制：** 数据大小不超过10MB，需要包含完整的分析结果  
**故障应对措施：** 上传失败时本地缓存数据，网络恢复后自动重传；数据格式错误时返回详细错误信息；服务器繁忙时延迟重试

### 3. 评估报告生成接口
**接口名称：** 评估报告生成接口  
**预期用户：** 医护人员  
**使用场景：** 需要查看和打印患者HRV评估报告时  
**预期用途：** 根据评估数据生成标准化的医疗报告，支持在线查看和打印  
**技术特征：** 动态HTML报告生成，支持图表展示，可导出PDF格式  
**使用限制：** 报告有效期30天，超期需要重新生成  
**故障应对措施：** 报告生成失败时提示重新生成；模板加载失败时使用备用模板；数据不完整时标注缺失项目

## 三、数据流程说明

1. **设备连接流程：** 客户端应用程序通过蓝牙接口请求与脉搏血氧饱和度仪通信，用于搜索、连接、传输数据并在软件界面上更新蓝牙设备连接状态指示和脉搏波形图。

2. **数据采集流程：** 设备连接成功后，开始实时采集PPG信号，进行信号质量检测和预处理，实时显示波形图。

3. **数据分析流程：** 采集完成后，对PPG数据进行HRV分析，计算时域参数（SDNN、RMSSD、pNN50等）和频域参数（VLF、LF、HF功率等）。

4. **数据上报流程：** 分析结果通过HTTPS接口上传到服务器，服务器验证数据完整性后存储到数据库。

5. **报告生成流程：** 服务器根据分析结果生成标准化报告，支持Web查看和PDF导出。

6. **报告打印流程：** 应用程序评估报告通过WIFI连接无线打印机，用于搜索、连接、传输打印数据并通过打印机打印报告。
