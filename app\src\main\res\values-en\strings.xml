<resources>
    <string name="app_name">HRV Stress Resilience Software</string>

    <string name="str_please_enter_account_number">Enter Username</string>
    <string name="str_please_enter_password">Enter Password</string>
    <string name="str_login">Login</string>
    <string name="str_evaluation_management">Evaluation Management</string>
    <string name="str_emr_management">EMR Management</string>
    <string name="str_search_report_id_name_phone">Search by File ID/Name/Phone</string>
    <string name="str_search_report_id_file_id_name">Search by Report ID/File ID/Name</string>
    <string name="str_new_medical_record">New medical record</string>
    <string name="str_edit_medical_record">Edit medical record</string>
    <string name="str_emr_id">EMR ID</string>
    <string name="str_name">Name</string>
    <string name="str_gender">Gender</string>
    <string name="str_age">Age</string>
    <string name="str_phone">Phone</string>
    <string name="str_creation_date">Creation Date</string>
    <string name="str_revision_date">Revision Date</string>
    <string name="str_operate">Operate</string>
    <string name="str_evaluation">Evaluation</string>
    <string name="str_edit">Edit</string>
    <string name="str_delete">Delete</string>
    <string name="str_male">Male</string>
    <string name="str_female">Female</string>
    <string name="str_birthday">Birthday</string>
    <string name="str_cancel">Cancel</string>
    <string name="str_ok">Confirm</string>
    <string name="str_commit">Commit</string>
    <string name="str_terminate_evaluation">Terminate</string>
    <string name="str_retake_evaluation">Retake</string>
    <string name="str_invalid_name_input">Invalid name input</string>
    <string name="str_invalid_age_input">Invalid age input</string>
    <string name="str_invalid_phone_number_format">Invalid phone number format</string>
    <string name="str_report_id">Report ID</string>
    <string name="str_evaluation_date">Evaluation Date</string>
    <string name="str_view">View</string>
    <string name="str_id_s">ID：%1$s</string>
    <string name="str_name_s">Name：%1$s</string>
    <string name="str_age_d">Age：%1$d</string>
    <string name="str_evaluation_date_s">Evaluation Date：%1$s</string>
    <string name="str_wear_device_properly">Please wear the device properly</string>
    <string name="str_scan">Scan</string>
    <string name="str_spo2_d">SPO2：%1$d%%</string>
    <string name="str_pr_d">PR：%1$d/min</string>
    <string name="str_pi_f">PI：%.1f%%</string>
    <string name="str_new_evaluation">New Evaluation</string>
    <string name="str_connected">Connected</string>
    <string name="str_disconnected">Disconnected</string>
    <string name="str_product_information">Product Information</string>
    <string name="str_registration_number">Registration Number</string>
    <string name="str_ml_number">ML Number</string>
    <string name="str_mah_manufacturer">MAH &amp; Manufacturer</string>
    <string name="str_mah_mfr_address">MAH &amp; Mfr. Address</string>
    <string name="str_mfg_site">Mfg. Site</string>
    <string name="str_product_name">Product Name</string>
    <string name="str_product_model">Product Model</string>
    <string name="str_version">Version</string>
    <string name="str_version_complete">Version Complete</string>
    <string name="str_service_life">Service Life</string>
    <string name="str_other_info">See the instruction for other contents</string>
    <string name="str_contact_info">Contact Information</string>
    <string name="str_airdoc_technology_changsha">Airdoc Technology Co., Ltd., Changsha</string>
    <string name="str_mfr_address_value">Rm 503–506, 5/F, Bldg B. AutoTech Park II, 13 Huanlian Rd. Changsha Hi-Tech Zone, 410205</string>
    <string name="str_mfg_site_value">Offices 501–506, L5, Bldg B. Grids 3–13, AutoTech Park. 13 Huanlian Rd, Changsha Hi-Tech Zone</string>
    <string name="str_product_name_value">HRV Stress Resilience Software</string>
    <string name="str_personal_information">Personal Information</string>
    <string name="str_change_password">Change Password</string>
    <string name="str_log_out">Logout</string>
    <string name="str_original_password">Original Password</string>
    <string name="str_new_password">New Password</string>
    <string name="str_confirm_password">Confirm Password</string>
    <string name="str_new_password_confirmation_differ">New password and confirmation differ</string>
    <string name="str_password_8_to_32_characters">Password must be 8 to 32 characters</string>
    <string name="str_password_least_one_letters">Password must include at least one letters</string>
    <string name="str_password_least_one_number">Password must include at least one number</string>
    <string name="str_password_contains_invalid_characters">Password contains invalid characters</string>
    <string name="str_password_tips">Password: 8–32 characters, at least one letter and one digit, supports special characters.</string>
    <string name="str_original_password_empty">Original password is empty</string>
    <string name="str_password_modified_successfully">The password was modified successfully.Please log in again.</string>
    <string name="str_password_modified_failed">The password was modified failed</string>
    <string name="str_modification_user_information_successful">The modification of user information was successful</string>
    <string name="str_modification_user_information_failed">The modification of user information was failed</string>
    <string name="str_medical_record_modified_successfully">Medical record modified successfully</string>
    <string name="str_medical_record_modified_failed">Medical record modified failed</string>
    <string name="str_medical_record_added_successfully">Medical record added successfully</string>
    <string name="str_medical_record_added_failed">Medical record added failed</string>
    <string name="str_whether_delete_medical_record">Whether to delete the medical record</string>
    <string name="str_medical_record_delete_successfully">Medical record delete successfully</string>
    <string name="str_medical_record_delete_failed">Medical record delete failed</string>
    <string name="str_whether_delete_evaluation">Whether to delete the evaluation</string>
    <string name="str_evaluation_delete_successfully">Evaluation delete successfully</string>
    <string name="str_evaluation_delete_failed">Evaluation delete failed</string>
    <string name="str_login_successfully">Login successfully</string>
    <string name="str_login_failed">Login failed</string>
    <string name="str_logout_failed">Logout failed</string>
    <string name="str_get_medical_record_details_failed">Get medical record details failed</string>
    <string name="str_submission_successful">Submission successful</string>
    <string name="str_submission_failed">Submission failed</string>
    <string name="str_data_collection_succeeded">Data collection succeeded</string>
    <string name="str_data_collection_failed">Data collection failed</string>
    <string name="str_no_devices_available_searching">No devices available, searching…</string>
    <string name="str_start_collection">Start collection</string>
    <string name="str_no_data">No data</string>
    <string name="str_remember_username_password">Remember username and password</string>

</resources>