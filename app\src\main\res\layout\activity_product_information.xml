<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="#EFF3F6">

    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/icon_back_black_coarse"
        android:layout_marginTop="20dp"
        android:layout_marginStart="20dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:id="@+id/tv_product_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_product_information"
        android:textColor="@color/color_333333"
        android:textSize="18sp"
        android:layout_marginTop="20dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <TextView
        android:id="@+id/tv_product_name_key"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:text="@string/str_product_name"
        android:textSize="13sp"
        android:textColor="@color/color_333333"
        android:gravity="center_vertical|end"
        android:background="@drawable/product_info_key_bg"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"
        android:layout_marginStart="50dp"
        android:layout_marginTop="20dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_product_info"
        app:layout_constraintRight_toLeftOf="@+id/tv_product_name_value"
        app:layout_constraintHorizontal_chainStyle="spread"
        app:layout_constraintHorizontal_weight="1"/>

    <TextView
        android:id="@+id/tv_product_name_value"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:text="@string/str_product_name_value"
        android:textSize="13sp"
        android:textColor="@color/color_333333"
        android:gravity="center_vertical|start"
        android:background="@drawable/product_info_value_bg"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        android:layout_marginEnd="50dp"
        app:layout_constraintTop_toTopOf="@+id/tv_product_name_key"
        app:layout_constraintBottom_toBottomOf="@+id/tv_product_name_key"
        app:layout_constraintLeft_toRightOf="@+id/tv_product_name_key"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintHorizontal_weight="2"/>

    <TextView
        android:id="@+id/tv_product_model_key"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:text="@string/str_product_model"
        android:textSize="13sp"
        android:textColor="@color/color_333333"
        android:gravity="center_vertical|end"
        android:background="@drawable/product_info_key_bg"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"
        android:layout_marginStart="50dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_product_name_key"
        app:layout_constraintRight_toLeftOf="@+id/tv_product_model_value"
        app:layout_constraintHorizontal_chainStyle="spread"
        app:layout_constraintHorizontal_weight="1"/>

    <TextView
        android:id="@+id/tv_product_model_value"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:text="YT-RTS"
        android:textSize="13sp"
        android:textColor="@color/color_333333"
        android:gravity="center_vertical|start"
        android:background="@drawable/product_info_value_bg"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        android:layout_marginEnd="50dp"
        app:layout_constraintTop_toTopOf="@+id/tv_product_model_key"
        app:layout_constraintBottom_toBottomOf="@+id/tv_product_model_key"
        app:layout_constraintLeft_toRightOf="@+id/tv_product_model_key"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintHorizontal_weight="2"/>

    <TextView
        android:id="@+id/tv_mah_manufacturer_key"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:text="@string/str_mah_manufacturer"
        android:textSize="13sp"
        android:textColor="@color/color_333333"
        android:gravity="center_vertical|end"
        android:background="@drawable/product_info_key_bg"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"
        android:layout_marginStart="50dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_product_model_key"
        app:layout_constraintRight_toLeftOf="@+id/tv_mah_manufacturer_value"
        app:layout_constraintHorizontal_chainStyle="spread"
        app:layout_constraintHorizontal_weight="1"/>

    <TextView
        android:id="@+id/tv_mah_manufacturer_value"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:text="@string/str_airdoc_technology_changsha"
        android:textSize="13sp"
        android:textColor="@color/color_333333"
        android:gravity="center_vertical|start"
        android:background="@drawable/product_info_value_bg"
        android:paddingStart="20dp"
        android:layout_marginEnd="50dp"
        app:layout_constraintTop_toTopOf="@+id/tv_mah_manufacturer_key"
        app:layout_constraintBottom_toBottomOf="@+id/tv_mah_manufacturer_key"
        app:layout_constraintLeft_toRightOf="@+id/tv_mah_manufacturer_key"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintHorizontal_weight="2"/>

    <TextView
        android:id="@+id/tv_mah_mfr_address_key"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:text="@string/str_mah_mfr_address"
        android:textSize="13sp"
        android:textColor="@color/color_333333"
        android:gravity="center_vertical|end"
        android:background="@drawable/product_info_key_bg"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"
        android:layout_marginStart="50dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_mah_manufacturer_key"
        app:layout_constraintRight_toLeftOf="@+id/tv_mah_mfr_address_value"
        app:layout_constraintHorizontal_chainStyle="spread"
        app:layout_constraintHorizontal_weight="1"/>

    <TextView
        android:id="@+id/tv_mah_mfr_address_value"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:text="@string/str_mfr_address_value"
        android:textSize="13sp"
        android:textColor="@color/color_333333"
        android:gravity="center_vertical|start"
        android:background="@drawable/product_info_value_bg"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        android:layout_marginEnd="50dp"
        app:layout_constraintTop_toTopOf="@+id/tv_mah_mfr_address_key"
        app:layout_constraintBottom_toBottomOf="@+id/tv_mah_mfr_address_key"
        app:layout_constraintLeft_toRightOf="@+id/tv_mah_mfr_address_key"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintHorizontal_weight="2"/>

    <TextView
        android:id="@+id/tv_mfg_site_key"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:text="@string/str_mfg_site"
        android:textSize="13sp"
        android:textColor="@color/color_333333"
        android:gravity="center_vertical|end"
        android:background="@drawable/product_info_key_bg"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"
        android:layout_marginStart="50dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_mah_mfr_address_key"
        app:layout_constraintRight_toLeftOf="@+id/tv_mfg_site_value"
        app:layout_constraintHorizontal_chainStyle="spread"
        app:layout_constraintHorizontal_weight="1"/>

    <TextView
        android:id="@+id/tv_mfg_site_value"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:text="@string/str_mfg_site_value"
        android:textSize="13sp"
        android:textColor="@color/color_333333"
        android:gravity="center_vertical|start"
        android:background="@drawable/product_info_value_bg"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        android:layout_marginEnd="50dp"
        app:layout_constraintTop_toTopOf="@+id/tv_mfg_site_key"
        app:layout_constraintBottom_toBottomOf="@+id/tv_mfg_site_key"
        app:layout_constraintLeft_toRightOf="@+id/tv_mfg_site_key"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintHorizontal_weight="2"/>

    <TextView
        android:id="@+id/tv_registration_number_key"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:text="@string/str_registration_number"
        android:textSize="13sp"
        android:textColor="@color/color_333333"
        android:gravity="center_vertical|end"
        android:background="@drawable/product_info_key_bg"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"
        android:layout_marginStart="50dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_mfg_site_key"
        app:layout_constraintRight_toLeftOf="@+id/tv_registration_number_value"
        app:layout_constraintHorizontal_chainStyle="spread"
        app:layout_constraintHorizontal_weight="1"/>

    <TextView
        android:id="@+id/tv_registration_number_value"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:textSize="13sp"
        android:textColor="@color/color_333333"
        android:text="湘械注准XXXXXXXXXXX"
        android:gravity="center_vertical|start"
        android:background="@drawable/product_info_value_bg"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        android:layout_marginEnd="50dp"
        app:layout_constraintTop_toTopOf="@+id/tv_registration_number_key"
        app:layout_constraintBottom_toBottomOf="@+id/tv_registration_number_key"
        app:layout_constraintLeft_toRightOf="@+id/tv_registration_number_key"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintHorizontal_weight="2"/>

    <TextView
        android:id="@+id/tv_ml_number_key"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:text="@string/str_ml_number"
        android:textSize="13sp"
        android:textColor="@color/color_333333"
        android:gravity="center_vertical|end"
        android:background="@drawable/product_info_key_bg"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"
        android:layout_marginStart="50dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_registration_number_value"
        app:layout_constraintRight_toLeftOf="@+id/tv_ml_number_value"
        app:layout_constraintHorizontal_chainStyle="spread"
        app:layout_constraintHorizontal_weight="1"/>

    <TextView
        android:id="@+id/tv_ml_number_value"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:textSize="13sp"
        android:textColor="@color/color_333333"
        android:text="湘药监械生产许20220199号"
        android:gravity="center_vertical|start"
        android:background="@drawable/product_info_value_bg"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        android:layout_marginEnd="50dp"
        app:layout_constraintTop_toTopOf="@+id/tv_ml_number_key"
        app:layout_constraintBottom_toBottomOf="@+id/tv_ml_number_key"
        app:layout_constraintLeft_toRightOf="@+id/tv_ml_number_key"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintHorizontal_weight="2"/>

    <TextView
        android:id="@+id/tv_version_key"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:text="@string/str_version"
        android:textSize="13sp"
        android:textColor="@color/color_333333"
        android:gravity="center_vertical|end"
        android:background="@drawable/product_info_key_bg"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"
        android:layout_marginStart="50dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_ml_number_key"
        app:layout_constraintRight_toLeftOf="@+id/tv_version_value"
        app:layout_constraintHorizontal_chainStyle="spread"
        app:layout_constraintHorizontal_weight="1"/>

    <TextView
        android:id="@+id/tv_version_value"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:text="V1"
        android:textSize="13sp"
        android:textColor="@color/color_333333"
        android:gravity="center_vertical|start"
        android:background="@drawable/product_info_value_bg"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        android:layout_marginEnd="50dp"
        app:layout_constraintTop_toTopOf="@+id/tv_version_key"
        app:layout_constraintBottom_toBottomOf="@+id/tv_version_key"
        app:layout_constraintLeft_toRightOf="@+id/tv_version_key"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintHorizontal_weight="2"/>

    <TextView
        android:id="@+id/tv_version_complete_key"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:text="@string/str_version_complete"
        android:textSize="13sp"
        android:textColor="@color/color_333333"
        android:gravity="center_vertical|end"
        android:background="@drawable/product_info_key_bg"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"
        android:layout_marginStart="50dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_version_key"
        app:layout_constraintRight_toLeftOf="@+id/tv_version_complete_value"
        app:layout_constraintHorizontal_chainStyle="spread"
        app:layout_constraintHorizontal_weight="1"/>

    <TextView
        android:id="@+id/tv_version_complete_value"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:text="V1.0.2025.06.30.01"
        android:textSize="13sp"
        android:textColor="@color/color_333333"
        android:gravity="center_vertical|start"
        android:background="@drawable/product_info_value_bg"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        android:layout_marginEnd="50dp"
        app:layout_constraintTop_toTopOf="@+id/tv_version_complete_key"
        app:layout_constraintBottom_toBottomOf="@+id/tv_version_complete_key"
        app:layout_constraintLeft_toRightOf="@+id/tv_version_complete_key"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintHorizontal_weight="2"/>

    <TextView
        android:id="@+id/tv_service_life_key"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:text="@string/str_service_life"
        android:textSize="13sp"
        android:textColor="@color/color_333333"
        android:gravity="center_vertical|end"
        android:background="@drawable/product_info_key_bg"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"
        android:layout_marginStart="50dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_version_complete_key"
        app:layout_constraintRight_toLeftOf="@+id/tv_service_life_value"
        app:layout_constraintHorizontal_chainStyle="spread"
        app:layout_constraintHorizontal_weight="1"/>

    <TextView
        android:id="@+id/tv_service_life_value"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:text="6年"
        android:textSize="13sp"
        android:textColor="@color/color_333333"
        android:gravity="center_vertical|start"
        android:background="@drawable/product_info_value_bg"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        android:layout_marginEnd="50dp"
        app:layout_constraintTop_toTopOf="@+id/tv_service_life_key"
        app:layout_constraintBottom_toBottomOf="@+id/tv_service_life_key"
        app:layout_constraintLeft_toRightOf="@+id/tv_service_life_key"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintHorizontal_weight="2"/>


    <TextView
        android:id="@+id/tv_contact_info_key"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:text="@string/str_contact_info"
        android:textSize="13sp"
        android:textColor="@color/color_333333"
        android:gravity="center_vertical|end"
        android:background="@drawable/product_info_key_bg"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"
        android:layout_marginStart="50dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_other_key"
        app:layout_constraintRight_toLeftOf="@+id/tv_contact_info_value"
        app:layout_constraintHorizontal_chainStyle="spread"
        app:layout_constraintHorizontal_weight="1"/>

    <TextView
        android:id="@+id/tv_contact_info_value"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:text="400-1003-999"
        android:textSize="13sp"
        android:textColor="@color/color_333333"
        android:gravity="center_vertical|start"
        android:background="@drawable/product_info_value_bg"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        android:layout_marginEnd="50dp"
        app:layout_constraintTop_toTopOf="@+id/tv_contact_info_key"
        app:layout_constraintBottom_toBottomOf="@+id/tv_contact_info_key"
        app:layout_constraintLeft_toRightOf="@+id/tv_contact_info_key"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintHorizontal_weight="2"/>

    <TextView
        android:id="@+id/tv_other_key"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:text="@string/str_other_info"
        android:textSize="13sp"
        android:textColor="#0000FF"
        android:gravity="center_vertical|end"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"
        android:layout_marginStart="50dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_service_life_key"
        app:layout_constraintRight_toLeftOf="@+id/tv_other_value"
        app:layout_constraintHorizontal_chainStyle="spread"
        app:layout_constraintHorizontal_weight="1"/>

    <TextView
        android:id="@+id/tv_other_value"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:textSize="13sp"
        android:textColor="@color/color_333333"
        android:gravity="center_vertical|start"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        android:layout_marginEnd="50dp"
        app:layout_constraintTop_toTopOf="@+id/tv_other_key"
        app:layout_constraintBottom_toBottomOf="@+id/tv_other_key"
        app:layout_constraintLeft_toRightOf="@+id/tv_other_key"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintHorizontal_weight="2"/>


</androidx.constraintlayout.widget.ConstraintLayout>